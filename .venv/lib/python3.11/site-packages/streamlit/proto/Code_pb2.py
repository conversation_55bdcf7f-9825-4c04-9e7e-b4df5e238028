# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/Code.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1astreamlit/proto/Code.proto\"n\n\x04\x43ode\x12\x11\n\tcode_text\x18\x01 \x01(\t\x12\x10\n\x08language\x18\x02 \x01(\t\x12\x19\n\x11show_line_numbers\x18\x03 \x01(\x08\x12\x12\n\nwrap_lines\x18\x04 \x01(\x08\x12\x12\n\x06height\x18\x05 \x01(\rB\x02\x18\x01\x42)\n\x1c\x63om.snowflake.apps.streamlitB\tCodeProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.Code_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\tCodeProto'
  _globals['_CODE'].fields_by_name['height']._loaded_options = None
  _globals['_CODE'].fields_by_name['height']._serialized_options = b'\030\001'
  _globals['_CODE']._serialized_start=30
  _globals['_CODE']._serialized_end=140
# @@protoc_insertion_point(module_scope)
