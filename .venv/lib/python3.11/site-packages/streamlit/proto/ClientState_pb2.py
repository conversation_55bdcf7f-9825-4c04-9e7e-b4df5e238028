# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: streamlit/proto/ClientState.proto
# Protobuf Python Version: 5.26.1
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from streamlit.proto import WidgetStates_pb2 as streamlit_dot_proto_dot_WidgetStates__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!streamlit/proto/ClientState.proto\x1a\"streamlit/proto/WidgetStates.proto\"\xf3\x01\n\x0b\x43ontextInfo\x12\x15\n\x08timezone\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x0ftimezone_offset\x18\x02 \x01(\x05H\x01\x88\x01\x01\x12\x13\n\x06locale\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x10\n\x03url\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x18\n\x0bis_embedded\x18\x05 \x01(\x08H\x04\x88\x01\x01\x12\x19\n\x0c\x63olor_scheme\x18\x06 \x01(\tH\x05\x88\x01\x01\x42\x0b\n\t_timezoneB\x12\n\x10_timezone_offsetB\t\n\x07_localeB\x06\n\x04_urlB\x0e\n\x0c_is_embeddedB\x0f\n\r_color_scheme\"\xe5\x01\n\x0b\x43lientState\x12\x14\n\x0cquery_string\x18\x01 \x01(\t\x12$\n\rwidget_states\x18\x02 \x01(\x0b\x32\r.WidgetStates\x12\x18\n\x10page_script_hash\x18\x03 \x01(\t\x12\x11\n\tpage_name\x18\x04 \x01(\t\x12\x13\n\x0b\x66ragment_id\x18\x05 \x01(\t\x12\x15\n\ris_auto_rerun\x18\x06 \x01(\x08\x12\x1d\n\x15\x63\x61\x63hed_message_hashes\x18\x07 \x03(\t\x12\"\n\x0c\x63ontext_info\x18\x08 \x01(\x0b\x32\x0c.ContextInfoB0\n\x1c\x63om.snowflake.apps.streamlitB\x10\x43lientStateProtob\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'streamlit.proto.ClientState_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\034com.snowflake.apps.streamlitB\020ClientStateProto'
  _globals['_CONTEXTINFO']._serialized_start=74
  _globals['_CONTEXTINFO']._serialized_end=317
  _globals['_CLIENTSTATE']._serialized_start=320
  _globals['_CLIENTSTATE']._serialized_end=549
# @@protoc_insertion_point(module_scope)
