#!/usr/bin/env python
# coding: utf-8

# #### Functions Examples
# 

# #### Example 1: Temperature Conversion

# In[1]:


def convert_temperature(temp,unit):
    """This function converts temperature between Celsius and Fahrenheit"""
    if unit=='C':
        return temp * 9/5 + 32  ## Celsius To Fahrenheit
    elif unit=="F":
        return (temp-32)*5/9 ## Fahrenheit to celsius
    else:
        return None

print(convert_temperature(25,'C'))
print(convert_temperature(77,'F'))


# ##### Example 2: Password Strength Checker

# In[2]:


def is_strong_password(password):
    """This function checks if the password is strong or not"""
    if len(password)<8:
        return False
    if not any(char.isdigit() for char in password):
        return False
    if not any(char.islower() for char in password):
        return False
    if not any(char.isupper() for char in password):
        return False
    if not any(char in '!@#$%^&*()_+' for char in password):
        return False
    return True

## calling the function
print(is_strong_password("WeakPwd"))
print(is_strong_password("Str0ngPwd!"))




# ##### Example 3: Calculate the Total Cost Of Items In a Shopping Cart

# In[4]:


def calculate_total_cost(cart):
    total_cost=0
    for item in cart:
        total_cost+=item['price']* item['quantity']

    return total_cost


## Example cart data

cart=[
    {'name':'Apple','price':0.5,'quantity':4},
    {'name':'Banana','price':0.3,'quantity':6},
    {'name':'Orange','price':0.7,'quantity':3}

]

## calling the function
total_cost=calculate_total_cost(cart)
print(total_cost)


# ##### Example 4: Check IF a String Is Palindrome

# In[5]:


def is_palindrome(s):
    s=s.lower().replace(" ","")
    return s==s[::-1]

print(is_palindrome("A man a plan a canal Panama"))
print(is_palindrome("Hello"))


# ##### Example 5: Calculate the factorials of a number using recursion

# In[7]:


def factorial(n):
    if n==0:
        return 1
    else:
        return n * factorial(n-1)

print(factorial(6))


# ##### Example 6: A Function To Read A File and count the frequency of each word

# In[10]:


def count_word_frequency(file_path):
    word_count={}
    with open(file_path,'r') as file:
        for line in file:
            words=line.split()
            for word in words:
                word=word.lower().strip('.,!?;:"\'')
                word_count[word]=word_count.get(word,0)+1

    return word_count

filepath='sample.txt'
word_frequency=count_word_frequency(filepath)
print(word_frequency)


# ##### Example 7: Validate Email Address

# In[11]:


import re

# Email validation function
def is_valid_email(email):
    """This function checks if the email is valid."""
    pattern = r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    return re.match(pattern, email) is not None

# Calling the function
print(is_valid_email("<EMAIL>"))  # Output: True
print(is_valid_email("invalid-email"))  # Output: False


# In[ ]:




