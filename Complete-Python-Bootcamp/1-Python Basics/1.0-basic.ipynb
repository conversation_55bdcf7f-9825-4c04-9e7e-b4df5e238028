## Basic Syntax Rules In Python
## Case sensitivity- Python is case sensitive

name="<PERSON><PERSON>"
Name="<PERSON><PERSON>"

print(name)
print(Name)


## Indentation
## Python uses indentation to define blocks of code. Consistent use of spaces (commonly 4) or a tab is required.

age=32
if age>30:
    
    print(age)
    
print(age)


## This is a single line comment
print("Hello World")

## Line Continuation
##Use a backslash (\) to continue a statement to the next line

total=1+2+3+4+5+6+7+\
4+5+6

print(total)


## Multiple Statements on a single line
x=5;y=10;z=x+y
print(z)

##Understand  Semnatics In Python
# variable assignment
age=32 ##age is an integer
name="<PERSON><PERSON>" ##name is a string



type(age)

type(name)

## Type Inference
variable=10
print(type(variable))
variable="Krish"
print(type(variable))

age=32
if age>30:
    print(age)

## Name Error
a=b

## Code exmaples of indentation
if True:
    print("Correct Indentation")
    if False:
        print("This ont print")
    print("This will print")
print("Outside the if block")