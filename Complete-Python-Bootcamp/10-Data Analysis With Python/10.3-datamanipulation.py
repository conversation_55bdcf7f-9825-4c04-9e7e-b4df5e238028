#!/usr/bin/env python
# coding: utf-8

# #### Data Manipulation and Analysis with Pandas
# Data manipulation and analysis are key tasks in any data science or data analysis project. Pandas provides a wide range of functions for data manipulation and analysis, making it easier to clean, transform, and extract insights from data. In this lesson, we will cover various data manipulation and analysis techniques using Pandas.

# In[1]:


import pandas as pd


# In[2]:


df=pd.read_csv('data.csv')
## fecth the first 5 rows
df.head(5)


# In[3]:


df.tail(5)


# In[4]:


df.describe()


# In[5]:


df.dtypes


# In[10]:


## Handling Missing Values
df.isnull().any()


# In[11]:


df.isnull().sum()


# In[13]:


df_filled=df.fillna(0)


# In[14]:


### filling missing values with the mean of the column
df['Sales_fillNA']=df['Sales'].fillna(df['Sales'].mean())
df


# In[15]:


df.dtypes


# In[18]:


## Renaming Columns
df=df.rename(columns={'Sale Date':'Sales Date'})
df.head()


# In[21]:


## change datatypes
df['Value_new']=df['Value'].fillna(df['Value'].mean()).astype(int)
df.head()


# In[22]:


df['New Value']=df['Value'].apply(lambda x:x*2)
df.head()


# In[23]:


## Data Aggregating And Grouping
df.head()


# In[24]:


grouped_mean=df.groupby('Product')['Value'].mean()
print(grouped_mean)


# In[25]:


grouped_sum=df.groupby(['Product','Region'])['Value'].sum()
print(grouped_sum)


# In[26]:


df.groupby(['Product','Region'])['Value'].mean()


# In[27]:


## aggregate multiple functions
groudped_agg=df.groupby('Region')['Value'].agg(['mean','sum','count'])
groudped_agg


# In[28]:


### Merging and joining Dataframes
# Create sample DataFrames
df1 = pd.DataFrame({'Key': ['A', 'B', 'C'], 'Value1': [1, 2, 3]})
df2 = pd.DataFrame({'Key': ['A', 'B', 'D'], 'Value2': [4, 5, 6]})


# In[29]:


df1


# In[30]:


df2


# In[31]:


## Merge Datafranme on the 'Key Columns'
pd.merge(df1,df2,on="Key",how="inner")


# In[32]:


pd.merge(df1,df2,on="Key",how="outer")


# In[33]:


pd.merge(df1,df2,on="Key",how="left")


# In[34]:


pd.merge(df1,df2,on="Key",how="right")


# In[ ]:




