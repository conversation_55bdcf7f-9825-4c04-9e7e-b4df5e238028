#!/usr/bin/env python
# coding: utf-8

# #### Pandas-DataFrame And Series
# Pandas is a powerful data manipulation library in Python, widely used for data analysis and data cleaning. It provides two primary data structures: Series and DataFrame. A Series is a one-dimensional array-like object, while a DataFrame is a two-dimensional, size-mutable, and potentially heterogeneous tabular data structure with labeled axes (rows and columns).

# In[1]:


import pandas as pd


# In[3]:


## Series
##A Pandas Series is a one-dimensional array-like object that can hold any data type. It is similar to a column in a table.

import pandas as pd
data=[1,2,3,4,5]
series=pd.Series(data)
print("Series \n",series)
print(type(series))


# In[4]:


## Create a Series from dictionary
data={'a':1,'b':2,'c':3}
series_dict=pd.Series(data)
print(series_dict)


# In[5]:


data=[10,20,30]
index=['a','b','c']
pd.Series(data,index=index)


# In[15]:


## Dataframe
## create a Dataframe from a dictionary oof list

data={
    'Name':['<PERSON>h','<PERSON>','<PERSON>'],
    'Age':[25,30,45],
    'City':['Bangalore','New York','Florida']
}
df=pd.DataFrame(data)
print(df)
print(type(df))


# In[8]:


## Create a Data frame From a List of Dictionaries

data=[
    {'Name':'Krish','Age':32,'City':'Bangalore'},
    {'Name':'John','Age':34,'City':'Bangalore'},
    {'Name':'Bappy','Age':32,'City':'Bangalore'},
    {'Name':'JAck','Age':32,'City':'Bangalore'}

]
df=pd.DataFrame(data)
print(df)
print(type(df))


# In[13]:


df=pd.read_csv('sales_data.csv')
df.head(5)


# In[11]:


df.tail(5)


# In[17]:


### Accessing Data From Dataframe
df


# In[20]:


df['Name']


# In[24]:


df.loc[0]


# In[30]:


df.iloc[0]


# In[34]:


df


# In[35]:


## Accessing a specified element
df.at[2,'Age']


# In[36]:


df.at[2,'Name']


# In[38]:


## Accessing a specified element using iat
df.iat[2,2]


# In[39]:


df


# In[40]:


### Data MAnipulation with Dataframe
df


# In[41]:


## Adding a column
df['Salary']=[50000,60000,70000]
df


# In[46]:


## Remove a column
df.drop('Salary',axis=1,inplace=True)


# In[47]:


df


# In[48]:


## Add age to the column
df['Age']=df['Age']+1
df


# In[51]:


df.drop(0,inplace=True)


# In[52]:


df


# In[53]:


df=pd.read_csv('sales_data.csv')
df.head(5)


# In[54]:


# Display the data types of each column
print("Data types:\n", df.dtypes)

# Describe the DataFrame
print("Statistical summary:\n", df.describe())



# In[55]:


df.describe()


# In[ ]:




