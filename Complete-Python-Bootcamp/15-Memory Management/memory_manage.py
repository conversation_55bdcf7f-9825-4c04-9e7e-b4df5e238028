#!/usr/bin/env python
# coding: utf-8

# #### Python Memory Management
# Memory management in Python involves a combination of automatic garbage collection, reference counting, and various internal optimizations to efficiently manage memory allocation and deallocation. Understanding these mechanisms can help developers write more efficient and robust applications.
# 
# 1. Key Concepts in Python Memory Management
# 2. Memory Allocation and Deallocation
# 3. Reference Counting
# 4. Garbage Collection
# 5. The gc Module
# 6. Memory Management Best Practices

# #### Reference Counting
# Reference counting is the primary method Python uses to manage memory. Each object in Python maintains a count of references pointing to it. When the reference count drops to zero, the memory occupied by the object is deallocated.

# In[1]:


import sys

a=[]
## 2 (one reference from 'a' and one from getrefcount())
print(sys.getrefcount(a))


# In[2]:


b=a
print(sys.getrefcount(b))


# In[5]:


del b
print(sys.getrefcount(a))


# #### Garbage Collection
# Python includes a cyclic garbage collector to handle reference cycles. Reference cycles occur when objects reference each other, preventing their reference counts from reaching zero.

# In[6]:


import gc
## enable garbage collection
gc.enable()



# In[7]:


gc.disable()


# In[8]:


gc.collect()


# In[9]:


### Get garbage collection stats
print(gc.get_stats())


# In[10]:


### get unreachable objects
print(gc.garbage)


# ####  Memory Management Best Practices
# 1. Use Local Variables: Local variables have a shorter lifespan and are freed sooner than global variables.
# 2. Avoid Circular References: Circular references can lead to memory leaks if not properly managed.
# 3. Use Generators: Generators produce items one at a time and only keep one item in memory at a time, making them memory efficient.
# 4. Explicitly Delete Objects: Use the del statement to delete variables and objects explicitly.
# 5. Profile Memory Usage: Use memory profiling tools like tracemalloc and memory_profiler to identify memory leaks and optimize memory usage.

# In[34]:


## Handled Circular reference
import gc

class MyObject:
    def __init__(self, name):
        self.name = name
        print(f"Object {self.name} created")

    def __del__(self):
        print(f"Object {self.name} deleted")

# Create circular reference
obj1 = MyObject("obj1")
obj2 = MyObject("obj2")
obj1.ref = obj2
obj2.ref = obj1

del obj1
del obj2

## Manually trigger the garbage collection
gc.collect()







# In[36]:


## Generators For Memory Efficiency
#Generators allow you to produce items one at a time, using memory efficiently by only keeping one item in memory at a time.

def generate_numbers(n):
    for i in range(n):
        yield i

## using the generator
for num in generate_numbers(100000):
    print(num)
    if num>10:
        break


# In[39]:


## Profiling Memory USage with tracemalloc
import tracemalloc

def create_list():
    return [i for i in range(10000)]

def main():
    tracemalloc.start()

    create_list()

    snapshot = tracemalloc.take_snapshot()
    top_stats = snapshot.statistics('lineno')

    print("[ Top 10 ]")
    for stat in top_stats[::]:
        print(stat)


# In[40]:


main()


# In[ ]:




